'use client';

import { <PERSON><PERSON><PERSON>ircle, CheckCircle, Loader2 } from 'lucide-react';
import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';

import UploadFile from '@/components/home/<USER>/uploadNewFile';
import { Button } from '@/components/ui/Button';
import { useGenProWorkflow } from '@/contexts/GenProWorkflowContext';
import { GenProService } from '@/service/genpro.service';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setToastAlert } from '@/slices/metaDataSlice';

interface IngestionProps {
  onNext?: () => void;
  onPrevious?: () => void;
  setIngestionData?: any;
}

const Ingestion = ({ onNext, setIngestionData }: IngestionProps) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const genproService = useMemo(() => new GenProService(apiService), [apiService]);
  const { setUploadedFiles: setContextUploadedFiles } = useGenProWorkflow();
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isUploadCancelled, setIsUploadCancelled] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const MINIMUM_FILES_REQUIRED = 3;
  const MAXIMUM_FILES_ALLOWED = 3;

  const handleFiles = (files: File[]) => {
    if (files.length > MAXIMUM_FILES_ALLOWED) {
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'error',
        title: 'Too Many Files',
        content: `Maximum ${MAXIMUM_FILES_ALLOWED} files allowed. Cannot upload ${files.length} file(s).`,
      }));
      return;
    }

    setUploadedFiles(files);
  };

  const handelClear = () => {
    setUploadedFiles([]);
    setIsUploadCancelled(true); 
    setTimeout(() => {
      setIsUploadCancelled(false);
    }, 100);
  };

  const handleProceed = async () => {
    if (uploadedFiles.length < MINIMUM_FILES_REQUIRED) {
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'warning',
        title: 'Insufficient Files',
        content: `Please upload at least ${MINIMUM_FILES_REQUIRED} files to proceed. Currently have ${uploadedFiles.length}/${MINIMUM_FILES_REQUIRED} files.`,
      }));
      return;
    }

    if (uploadedFiles.length > MAXIMUM_FILES_ALLOWED) {
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'error',
        title: 'Too Many Files',
        content: `Maximum ${MAXIMUM_FILES_ALLOWED} files allowed. Please remove ${uploadedFiles.length - MAXIMUM_FILES_ALLOWED} file(s) before proceeding.`,
      }));
      return;
    }

    setIsProcessing(true);
    dispatch(setIsLoading(true));

    try {
      const response = await genproService.uploadWorkflowFiles(uploadedFiles, false);

      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'success',
        title: 'Files Uploaded Successfully',
        content: 'Files have been uploaded to the workflow system.',
      }));

      if (response.data) {
        setContextUploadedFiles(response.data);
        setIngestionData(response.data);
      }

      onNext?.();
    } catch (error: any) {
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'error',
        title: 'Upload Failed',
        content: error.response?.data?.error || 'Failed to upload files to workflow system',
      }));
    } finally {
      setIsProcessing(false);
      dispatch(setIsLoading(false));
    }
  };

  return (
    <div className="h-full flex flex-col overflow-hidden p-4">
      <div className="mb-3 flex min-h-0 flex-1 flex-col rounded border border-lightgray-100 bg-white-200">
        <div className="flex-1 overflow-hidden p-3">
          <UploadFile onFileUpload={handleFiles} isUploadCancled={isUploadCancelled} />
          
        </div>
      </div>

      <div className="shrink-0 rounded border border-lightgray-100 bg-white-200 p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {uploadedFiles.length >= MINIMUM_FILES_REQUIRED && uploadedFiles.length <= MAXIMUM_FILES_ALLOWED ? (
              <CheckCircle className="size-4 text-green-500" />
            ) : (
              <AlertCircle className="size-4 text-orange-500" />
            )}
            <span className={`text-xs font-medium ${
              uploadedFiles.length >= MINIMUM_FILES_REQUIRED && uploadedFiles.length <= MAXIMUM_FILES_ALLOWED ? 'text-green-600' : 'text-orange-600'
            }`}>
              {uploadedFiles.length >= MINIMUM_FILES_REQUIRED && uploadedFiles.length <= MAXIMUM_FILES_ALLOWED
                ? `${uploadedFiles.length} files attached ✓`
                : uploadedFiles.length > MAXIMUM_FILES_ALLOWED
                  ? `${uploadedFiles.length} files attached (Max ${MAXIMUM_FILES_ALLOWED})`
                  : `${uploadedFiles.length}/${MINIMUM_FILES_REQUIRED} files attached`
              }
            </span>
            {uploadedFiles.length < MINIMUM_FILES_REQUIRED && (
              <>
                <span className="text-xs text-gray-400">•</span>
                <span className="text-xs text-orange-600">
                  Need {MINIMUM_FILES_REQUIRED - uploadedFiles.length} more file(s)
                </span>
              </>
            )}
            {uploadedFiles.length > MAXIMUM_FILES_ALLOWED && (
              <>
                <span className="text-xs text-gray-400">•</span>
                <span className="text-xs text-red-600">
                  Remove {uploadedFiles.length - MAXIMUM_FILES_ALLOWED} file(s)
                </span>
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            {uploadedFiles.length > 0 && (
              <Button
                className="flex h-[32px] items-center px-3 text-xs"
                type="button"
                onClick={handelClear}
                intent="secondary"
              >
                Clear Files
              </Button>
            )}
            <Button
              className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
              type="submit"
              disabled={uploadedFiles.length < MINIMUM_FILES_REQUIRED || uploadedFiles.length > MAXIMUM_FILES_ALLOWED || isProcessing}
              onClick={handleProceed}
              aria-label={
                uploadedFiles.length < MINIMUM_FILES_REQUIRED
                  ? `Upload ${MINIMUM_FILES_REQUIRED - uploadedFiles.length} more file(s) to continue`
                  : uploadedFiles.length > MAXIMUM_FILES_ALLOWED
                    ? `Remove ${uploadedFiles.length - MAXIMUM_FILES_ALLOWED} file(s) to continue`
                    : 'Proceed to validation step'
              }
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  Processing...
                </>
              ) : uploadedFiles.length < MINIMUM_FILES_REQUIRED ? (
                `Upload ${MINIMUM_FILES_REQUIRED - uploadedFiles.length} More File(s)`
              ) : uploadedFiles.length > MAXIMUM_FILES_ALLOWED ? (
                `Remove ${uploadedFiles.length - MAXIMUM_FILES_ALLOWED} File(s)`
              ) : (
                'Proceed to Validation →'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Ingestion;