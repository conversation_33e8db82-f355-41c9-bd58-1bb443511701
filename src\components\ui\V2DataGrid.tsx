import "src/styles/dataGridStyle.css";
import "react-data-grid/lib/styles.css";

// import deleteIcon from "public/assets/images/platform-ops/deleteIcon.svg";
// import settingsIcon from "public/assets/images/platform-ops/settings_icon.svg";
import type { FC } from "react";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import type {
  Column,
  RenderHeaderCellProps,
  SortColumn,
} from "react-data-grid";
import DataGrid, {
  SelectCellFormatter,
  SelectColumn,
  textEditor,
} from "react-data-grid";

// import linkIcon from "@/public/assets/images/platform-ops/linkIcon.svg";
import { Radio } from "@mui/material";

import V2Badge from "./V2Badge";
import V2GroupedDropdowns from "./V2GroupedDropdowns";

import V2ChipSelect from "./V2ChipSelect";
import V2Selectbox from "./V2SelectBox";

interface headerProps {
  name: string;
  filterType: string;
  options?: string[];
  groupOptions?: any;
  isSortable?: boolean;
  isMenu?: boolean;
  isBadge?: boolean;
  width?: number;
  isLink?: boolean;
  isSelect?: boolean;
  isSingleSelect?: boolean;
  isFrozen?: boolean;
  isExpandable?: boolean;
  isDelete?: boolean;
  isInput?: boolean;
  isCheckBox?: boolean;
  isIcon?: boolean;
  isDropdown?: boolean;
  isBadgeDropdown?: boolean;
  placeholder?: string;
  handleOpenClick?: (data: any) => void;
  handleOnChange?: (e: any, index: number, key?: string) => void;
  isGroupedDropdown?: boolean;
  disabled?: boolean;
  renderCell?: ({ row }: any) => React.ReactNode;
}

interface V2DataGridProps {
  data: any;
  headerList: headerProps[];
  isLeft?: boolean;
  filters?: any;
  setFilters?: (data: any) => void;
  selectedRows?: ReadonlySet<number>;
  setSelectedRows?: (data: any) => void;
  setRows?: (data: any) => void;
  handleRowSelected?: (data: any, type?: string, rowIdx?: number) => void;
  className?: string;
}

const FilterContext = createContext<any>(undefined);

function inputStopPropagation(event: React.KeyboardEvent<HTMLInputElement>) {
  if (["ArrowLeft", "ArrowRight"].includes(event.key)) {
    event.stopPropagation();
  }
}

function rowKeyGetter(row: any) {
  return row.id;
}

const V2DataGrid: FC<V2DataGridProps> = ({
  data,
  headerList,
  filters = {},
  isLeft = false,
  setFilters = () => {},
  selectedRows = new Set<number>(),
  setSelectedRows,
  setRows,
  handleRowSelected = () => {},
  className = "",
}) => {
  const [header, setHeader] = useState<Column<any>[]>([]);
  const [sortColumns, setSortColumns] = useState<readonly SortColumn[]>([]);
  const onSortColumnsChange = useCallback((sortColumns: SortColumn[]) => {
    setSortColumns(sortColumns.slice(-1));
  }, []);

  const statusmapper: any = {
    Open: "success",
    Closed: "error",
    Approved: "success",
    "On Hold": "warning",
    Complete: "success",
  };

  const filteredRows = useMemo(() => {
    return data.filter((r: any) => {
      return headerList.every((header: headerProps) => {
        if (
          header.filterType === "search" ||
          header.filterType === "dropdown"
        ) {
          const filterValue = filters[header.name];
          if (typeof r[header.name] === "boolean") {
            if (filterValue === "True") return r[header.name] === true;
            if (filterValue === "False") return r[header.name] === false;
            return true;
          }
          return filterValue ? r[header.name]?.includes(filterValue) : true;
        }
        return true;
      });
    });
  }, [data, filters, headerList]);

  const sortedRows = useMemo((): readonly any[] => {
    if (sortColumns.length === 0) return data;

    const sortColumn = sortColumns[0];
    if (!sortColumn) return data;

    const { columnKey, direction } = sortColumn;

    let sortedRows: any[] = [...data];

    if (columnKey.length > 0) {
      sortedRows = sortedRows.sort((a, b) =>
        a[columnKey]?.localeCompare(b[columnKey])
      );
    }

    return direction === "DESC" ? sortedRows.reverse() : sortedRows;
  }, [data, sortColumns]);

  const [columnsOrder, setColumnsOrder] = useState((): readonly number[] =>
    header.map((_, index) => index)
  );

  function onColumnsReorder(sourceKey: string, targetKey: string) {
    // console.log(sourceKey, targetKey);
    setColumnsOrder((columnsOrder) => {
      const sourceColumnOrderIndex = columnsOrder.findIndex(
        (index) => header[index]?.key === sourceKey
      );
      const targetColumnOrderIndex = columnsOrder.findIndex(
        (index) => header[index]?.key === targetKey
      );

      if (sourceColumnOrderIndex === -1 || targetColumnOrderIndex === -1) {
        return columnsOrder;
      }

      const newColumnsOrder = [...columnsOrder];
      const [sourceColumnOrder] = newColumnsOrder.splice(
        sourceColumnOrderIndex,
        1
      );
      newColumnsOrder.splice(targetColumnOrderIndex, 0, sourceColumnOrder || 0);
      return newColumnsOrder as readonly number[];
    });
  }

  const reorderedColumns: any = useMemo(() => {
    return columnsOrder.map((index) => header[index]);
  }, [columnsOrder]);

  useEffect(() => {
    if (headerList?.length > 0) {
      const helperArray: any[] = [];
      headerList.forEach((each: headerProps) => {
        helperArray.push(
          each.isSelect && each.isSingleSelect
            ? {
                key: "select",
                name: "",
                width: 40,
                frozen: false,
                resizable: false,
                renderCell: ({ row }: any) => (
                  <div className="flex items-center justify-center">
                    <Radio
                      checked={
                        Array.isArray(selectedRows)
                          ? selectedRows[0] === row.id
                          : selectedRows instanceof Set
                            ? selectedRows.has(row.id)
                            : false
                      }
                      onClick={() => {
                        if (setSelectedRows) {
                          // mimic SelectColumn: pass a Set with the selected row id
                          setSelectedRows(new Set([row.id]));
                        }
                      }}
                      value={row.id}
                      size="small"
                      color="primary"
                      inputProps={{ "aria-label": "Select row" }}
                    />
                  </div>
                ),
              }
            : each.isSelect
              ? {
                  ...SelectColumn,
                  headerCellClass: "justify-center items-center flex",
                  cellClass: "justify-center items-center flex",
                  renderCell: (props: any) => (
                    <div className="flex items-center justify-center h-full">
                      {SelectColumn.renderCell &&
                        SelectColumn.renderCell(props)}
                    </div>
                  ),
                }
              : {
                  key: each.isMenu ? "menu" : each.name,
                  name: each.name,
                  width: each.width,
                  resizable:
                    each.isExpandable === undefined ? true : each.isExpandable,
                  frozen: each.isFrozen,
                  placeholder: each.placeholder,
                  draggable: true,
                  sortable: each.isSortable,
                  ...(each.filterType && {
                    renderHeaderCell: (p: any) => {
                      switch (each.filterType) {
                        case "search":
                          return (
                            <FilterRenderer<any> {...p}>
                              {({ filters, ...rest }) => (
                                <input
                                  {...rest}
                                  value={filters[each.name]}
                                  onChange={(e) => {
                                    // console.log(e.target.value);
                                    setFilters({
                                      ...filters,
                                      [each.name]: e.target.value,
                                    });
                                  }}
                                  onKeyDown={inputStopPropagation}
                                />
                              )}
                            </FilterRenderer>
                          );

                        case "dropdown":
                          return (
                            <FilterRenderer<any> {...p}>
                              {({ filters }) => {
                                return (
                                  <V2Selectbox
                                    placeholder={each.name}
                                    options={each.options || []}
                                    value={filters[each.name]}
                                    boxStyle="bg-white-200 w-full"
                                    onChange={(e: any) =>
                                      setFilters({
                                        ...filters,
                                        [each.name]: e.target.value,
                                      })
                                    }
                                  />
                                );
                              }}
                            </FilterRenderer>
                          );
                        case "groupDropdown":
                          return (
                            <FilterRenderer<any> {...p}>
                              {({ filters }) => {
                                return (
                                  <V2GroupedDropdowns
                                    placeholder={each.name}
                                    options={each.groupOptions}
                                    value={filters[each.name]}
                                    boxStyle="bg-white-200 w-full"
                                    onChange={(e: any) =>
                                      setFilters({
                                        ...filters,
                                        [each.name]: e.target.value,
                                      })
                                    }
                                  />
                                );
                              }}
                            </FilterRenderer>
                          );

                        default:
                          return null;
                      }
                    },
                  }),
                  ...(each.isInput && {
                    renderEditCell: textEditor,
                  }),
                  ...(each.isCheckBox && {
                    renderCell({ row, onRowChange, tabIndex }: any) {
                      return (
                        <SelectCellFormatter
                          value={row[each.name]}
                          onChange={() => {
                            onRowChange({
                              ...row,
                              [each.name]: !row[each.name],
                            });
                          }}
                          disabled={each.disabled}
                          tabIndex={tabIndex}
                        />
                      );
                    },
                  }),
                  ...(each.isBadge && {
                    renderCell({ row }: any) {
                      return (
                        <div
                          className="flex items-center justify-center"
                          key={row[each.name]}
                        >
                          <V2Badge
                            intent={statusmapper[row[each.name]]}
                            content={row[each.name]}
                          />
                        </div>
                      );
                    },
                  }),
                  ...(each.isIcon && {
                    renderCell({ row }: any) {
                      return (
                        <div className="flex items-center justify-center">
                          {/* <img
                          src={settingsIcon.src}
                          width="15px"
                          onClick={() => handleRowSelected(row, "setting")}
                          className="cursor-pointer"
                        /> */}
                        </div>
                      );
                    },
                  }),
                  ...(each.isLink && {
                    renderCell({ row }: any) {
                      return (
                        <div className="flex items-center justify-center">
                          {/* <img
                          src={linkIcon.src}
                          width="15px"
                          onClick={() => handleRowSelected(row, "link")}
                          className="cursor-pointer"
                        /> */}
                        </div>
                      );
                    },
                  }),
                  ...(each.isDelete && {
                    renderCell({ row, rowIdx }: any) {
                      return (
                        <div className="flex items-center justify-center">
                          {/* <img
                          src={deleteIcon.src}
                          width="15px"
                          onClick={() =>
                            handleRowSelected(row, "delete", rowIdx)
                          }
                          className="cursor-pointer"
                        /> */}
                        </div>
                      );
                    },
                  }),
                  ...(each.isBadgeDropdown && {
                    renderCell: (p: any) => {
                      return (
                        <V2ChipSelect
                          placeholder={
                            p.column.placeholder
                              ? p.column.placeholder
                              : p.column.key
                          }
                          options={each.options || []}
                          value={p.row[p.column.key]}
                          onOpen={() => {
                            if (each.handleOpenClick) {
                              each?.handleOpenClick(p.row);
                            }
                          }}
                          boxStyle="bg-white-200 w-full"
                          onChange={(e: any) => {
                            if (each.handleOnChange) {
                              each?.handleOnChange(e, p.rowIdx, p.column.key);
                            }
                          }}
                        />
                      );
                    },
                  }),
                  ...(each.isDropdown && {
                    renderCell: (p: any) => {
                      return (
                        <V2Selectbox
                          placeholder={
                            p.column.placeholder
                              ? p.column.placeholder
                              : p.column.key
                          }
                          options={each.options || []}
                          value={p.row[p.column.key]}
                          onOpen={() => {
                            if (each.handleOpenClick) {
                              each?.handleOpenClick(p.row);
                            }
                          }}
                          boxStyle="bg-white-200 w-full"
                          onChange={(e: any) => {
                            if (each.handleOnChange) {
                              each?.handleOnChange(e, p.rowIdx, p.column.key);
                            }
                          }}
                        />
                      );
                    },
                  }),
                  ...(each.isGroupedDropdown && {
                    renderCell: (p: any) => {
                      return (
                        <V2GroupedDropdowns
                          placeholder={p.column.key}
                          multiSelect
                          options={each.groupOptions || []}
                          value={p.row[p.column.key]}
                          selectedOptions={p.row[p.column.key] || []}
                          boxStyle="bg-white-200 w-full"
                          onChange={(e: any) => {
                            if (each.handleOnChange) {
                              each?.handleOnChange(e, p.rowIdx);
                            }
                          }}
                        />
                      );
                    },
                  }),
                  ...(each.renderCell && {
                    renderCell: each.renderCell,
                  }),
                }
        );
      });
      setHeader(helperArray);
      setColumnsOrder(helperArray.map((_, index) => index));
    }
  }, [headerList]);

  return (
    <div className="w-full h-full">
      <FilterContext.Provider value={filters}>
        <DataGrid
          className={`rdg-light ${
            filters?.enabled ? "filterContainerClassname" : ""
          } ${
            isLeft ? "text-left [&_.rdg-cell]:!p-1" : "text-center"
          } ${className}`}
          columns={reorderedColumns.length === 0 ? header : reorderedColumns}
          rows={filters.enabled ? filteredRows : sortedRows}
          headerRowHeight={filters?.enabled ? 70 : undefined}
          rowHeight={35}
          sortColumns={sortColumns}
          onSortColumnsChange={onSortColumnsChange}
          selectedRows={selectedRows}
          onSelectedRowsChange={setSelectedRows}
          rowKeyGetter={rowKeyGetter}
          onRowsChange={setRows}
          onColumnsReorder={onColumnsReorder}
          onCellClick={(args, event) => {
            if (args.column.key === "title") {
              event.preventGridDefault();
              // args.selectCell(true);
            }
          }}
        />
      </FilterContext.Provider>
    </div>
  );
};

export default V2DataGrid;

function FilterRenderer<R>({
  tabIndex,
  column,
  children,
}: RenderHeaderCellProps<R> & {
  children: (args: { tabIndex: number; filters: any }) => React.ReactElement;
}) {
  const filters = useContext(FilterContext)!;
  return (
    <>
      <div>{column.name}</div>
      {filters.enabled && (
        <div className="filter-cell">{children({ tabIndex, filters })}</div>
      )}
    </>
  );
}
