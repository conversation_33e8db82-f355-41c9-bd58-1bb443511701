"use client";

import React, { useState, useMemo } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Eye, Settings, Download, Loader2 } from "lucide-react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/Button";
import { Modal } from "@/components/ui/Modal";
import V2DataGrid from "@/components/ui/V2DataGrid";
import { APIService } from "@/service/api.service";
import { setToastAlert } from "@/slices/metaDataSlice";
import { ApiUtilities } from "@/utils/ApiUtilities";

interface TransformationProps {
  onNext?: () => void;
  uploadedFiles?: any;
  onTransformationComplete?: (data: any) => void;
  transformedData: any;
  setPivotData: any;
  ingestionData: any;
}

interface TransformationRule {
  id: number;
  rule_name: string;
  description: string;
  condition: string;
  action: string;
  status: string;
}

const LoadingSpinner = ({ text }: { text: string }) => (
  <div className="p-4 flex items-center justify-center">
    <div className="flex items-center space-x-2">
      <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
      <span className="text-gray-600">{text}</span>
    </div>
  </div>
);

const Transformation = ({
  onNext,
  uploadedFiles,
  onTransformationComplete,
  transformedData,
  setPivotData,
  ingestionData,
}: TransformationProps) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(
    () => new APIService(dispatch, router),
    [dispatch, router]
  );

  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  const transformationFileUrl = transformedData.result.transformation_file_url;

  // Mock transformation rules data
  const transformationRules: TransformationRule[] = [
    {
      id: 1,
      rule_name: "Solar to CYP Mapping",
      description: "Map all Solar entities to BSM CYP",
      condition: 'SMC contains "Solar"',
      action: "Map to BSM CYP",
      status: "Active",
    },
    {
      id: 2,
      rule_name: "SEACHEF Logic",
      description: "Apply 50% BF allocation for SEACHEF entities",
      condition: 'SMC equals "SEACHEF"',
      action: "Apply 50% BF allocation",
      status: "Active",
    },
    {
      id: 3,
      rule_name: "Frontline Management",
      description: "Keep original SMC for Frontline entities",
      condition: 'SMC equals "Frontline"',
      action: "Keep original SMC",
      status: "Active",
    },
    {
      id: 4,
      rule_name: "Default Case",
      description: "Standard mapping for all other entities",
      condition: "All other cases",
      action: "Apply standard mapping",
      status: "Active",
    },
  ];

  // Transform rules data for grid display
  const rulesGridData = transformationRules.map((rule) => ({
    id: rule.id,
    "RULE NAME": (
      <div className="flex items-center space-x-2">
        <Settings className="w-4 h-4 text-gray-500" />
        <span className="font-medium">{rule.rule_name}</span>
      </div>
    ),
    DESCRIPTION: rule.description,
    CONDITION: rule.condition,
    ACTION: rule.action,
    STATUS: (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700">
        {rule.status}
      </span>
    ),
  }));

  // Grid column configuration for rules
  const rulesHeaderList = [
    { key: "rule_name", name: "RULE NAME", width: 200, filterType: "" },
    { key: "description", name: "DESCRIPTION", width: 250, filterType: "" },
    { key: "condition", name: "CONDITION", width: 180, filterType: "" },
    { key: "action", name: "ACTION", width: 180, filterType: "" },
    { key: "status", name: "STATUS", filterType: "" },
  ];

  const advanceWorkflowStage = async (workflowRunId: string) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}/workflow-run/${workflowRunId}/next_stage/`;
    return await apiService.genproPostRequest(url, {});
  };

  const handleProceed = async () => {
    setSaveLoading(true);
    const workflowId = ingestionData?.workflow_run?.id;
    try {
      const response = await advanceWorkflowStage(workflowId);
      setPivotData(response.data);
      onTransformationComplete?.(uploadedFiles);
      onNext?.();
    } catch (error: any) {
      console.error("Transformation failed:", error);
    } finally {
      setSaveLoading(false);
    }
  };

  const handleViewResults = () => {
    setIsPreviewModalOpen(true);
  };

  const handleClosePreview = () => {
    setIsPreviewModalOpen(false);
  };

  // Generate iframe URL for file preview
  const getPreviewUrl = () => {
    if (transformationFileUrl) {
      const URLOFFICE = `https://view.officeapps.live.com/op/embed.aspx?src=`;
      return `${URLOFFICE}${encodeURIComponent(transformationFileUrl)}`;
    }
    return "";
  };

  return (
    <div className="flex flex-col h-full space-y-6 p-6">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-0">
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden flex-shrink-0">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800">
                Business Rules Applies
              </h2>
              <div className="flex space-x-2">
                {transformationFileUrl && (
                  <Button
                    onClick={handleViewResults}
                    className="flex items-center px-3 py-1.5 text-sm border border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    View Results
                  </Button>
                )}

                {/* Download Transformation Result Button */}
                {transformationFileUrl && (
                  <Button
                    onClick={() => window.open(transformationFileUrl, "_blank")}
                    className="flex items-center px-3 py-1.5 text-sm border border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <Download className="w-4 h-4 mr-1" />
                    Download Results
                  </Button>
                )}
              </div>
            </div>
          </div>

          <div className="p-4">
            {loading ? (
              <LoadingSpinner text="Loading transformation data..." />
            ) : (
              <V2DataGrid headerList={rulesHeaderList} data={rulesGridData} />
            )}
          </div>
        </div>
      </div>

      {/* Transformation Note */}
      <div className="shrink-0 mb-3">
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-center space-x-2">
            <Eye className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-800">
              {transformationFileUrl
                ? "Transformation results are ready. Click 'View Results' to preview the processed data or 'Download Results' to get the file."
                : "Transformation results will be available once processing is complete."}
            </span>
          </div>
        </div>
      </div>

      {/* Navigation Controls - Fixed Footer */}
      <div className="shrink-0 rounded border border-lightgray-100 bg-white-200 p-2">
        <div className="flex items-center justify-between">
          {/* Status Section */}
          <div className="flex items-center space-x-3">
            <CheckCircle className="size-4 text-green-500" />
            <span className="text-xs font-medium text-green-600">
              Transformation rules configured
            </span>
            <span className="text-xs text-gray-400">•</span>
            <span className="text-xs text-gray-600">
              {transformationRules.length} active rules |{" "}
              {transformationRules.reduce((sum, rule) => sum, 0)} total records
            </span>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button
              className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
              type="submit"
              onClick={handleProceed}
              disabled={saveLoading}
            >
              {saveLoading ? (
                <>
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  Processing...
                </>
              ) : (
                "Continue to Distribution →"
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* File Preview Modal */}
      <Modal
        isOpen={isPreviewModalOpen}
        closeModal={handleClosePreview}
        headerTitle="Transformation Results Preview"
        component={
          <div className="p-4">
            {transformationFileUrl ? (
              <div className="space-y-4">
                {/* File Preview */}
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <iframe
                    title="Transformation Results Preview"
                    width="100%"
                    height="600px"
                    src={getPreviewUrl()}
                    frameBorder="0"
                    allowFullScreen
                    className="w-full"
                  />
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">No file available for preview</p>
              </div>
            )}
          </div>
        }
        isActionButtonVisible={false}
        isCancelVisible={true}
        cancelbuttonText="Close"
        footerSecondaryEventHandler={handleClosePreview}
        panelWidth="w-[90vw] max-w-6xl"
      />
    </div>
  );
};

export default Transformation;
