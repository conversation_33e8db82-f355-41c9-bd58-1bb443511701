.rdg-cell[role='gridcell'] {
  background-color: #ffffff;
  color: #1f2937;
  outline: none;
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  overflow: hidden;
  padding: 2px 8px;
  border: none;
  border-bottom: 1px solid #e5e7eb;
  border-right: 1px solid #f3f4f6;
  transition: all 0.2s ease-in-out;
  line-height: 1.3;
  height: 20px;
  width: 100%;
}

.rdg-cell[role='gridcell']:hover {
  background-color: #f8fafc;
  color: #111827;
}


.rdg-cell[role='gridcell']::-webkit-scrollbar {
  display: none;
}

.rdg-checkbox-input {
  border-radius: 6px;
  border: 2px solid #d1d5db;
  width: 18px;
  height: 18px;
  transition: all 0.2s ease-in-out;
}

.rdg-checkbox-input:hover {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.rdg-checkbox-input:checked {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.rdg-text-editor {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  height: 38px;
  font-size: 13px;
  color: #374151;
  padding: 8px 12px;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.rdg-text-editor:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), 0 1px 2px rgba(0, 0, 0, 0.05);
  outline: none;
}

.filter-cell {
  line-height: 1.5;
  padding: 8px 0;
  display: flex;

  > div {
    padding: 0;
    width: 100%;
  }

  > input {
    height: 38px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    width: 100%;
    font-size: 13px;
    color: #374151;
    padding: 8px 12px;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  > input:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), 0 1px 2px rgba(0, 0, 0, 0.05);
    outline: none;
  }

  > input::placeholder {
    color: #9ca3af;
    font-weight: 400;
  }
}

.data-grid-toolbar {
  display: flex;
  gap: 8px;
  justify-content: end;
  padding: 16px 0;

  > button {
    font-size: 13px;
    font-weight: 500;
    border: 2px solid #2563eb;
    border-radius: 8px;
    color: #2563eb;
    background-color: #ffffff;
    padding: 10px 20px;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  > button:hover {
    background-color: #2563eb;
    color: #ffffff;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.2);
    transform: translateY(-1px);
  }

  > button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
  }
}

.rdg::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.rdg::-webkit-scrollbar-track {
  background-color: #f9fafb;
  border-radius: 8px;
}

.rdg::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #e5e7eb, #d1d5db);
  border-radius: 8px;
  border: 2px solid #f9fafb;
}

.rdg::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #d1d5db, #9ca3af);
}

.rdg::-webkit-scrollbar-corner {
  background-color: #f9fafb;
}

.rdg-cell[role='columnheader'] {
  background-color: #f0f0f0;
  font-weight: 700;
  color: #2c3e50;
  font-size: 11px;
  border: none;
  border-bottom: 2px solid #d0d0d0;
  border-right: 1px solid #d0d0d0;
  outline: none;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: all 0.2s ease-in-out;
  letter-spacing: 0.025em;
  height: 32px;
  padding: 4px 8px;
  text-transform: uppercase;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  width:100%
}

.rdg-cell[role='columnheader']:hover {
  background-color: #e8e8e8;
  color: #1a252f;
}

.rdg {
  border-radius: 12px;
  background-color: #ffffff;
  height: fit-content;
  max-height: 100%;
  overflow: auto;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02), 0 1px 3px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease-in-out;
}

.rdg:hover {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.04), 0 4px 6px rgba(0, 0, 0, 0.1);
}