/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */

'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import { setIsLoading } from '@/slices/appSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

const FilePreview: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const localService = new LocalService();
  // Derivatives
  const searchParams = useSearchParams();
  const fileId: any = searchParams?.get('fileId');
  const source: any = searchParams?.get('source') || 'upload'; // 'upload' or 'genpro'
  
  const [urlIframe, setUrlIframe] = useState<any>('');
  const [fileDetail, setFileDetail] = useState<any>(null);
  const URLOFFICE = `https://view.officeapps.live.com/op/embed.aspx?src=`;
  
  const goBack = () => {
    if (source === 'genpro') {
      window.close();
    } else {
      localStorage.removeItem('fileDetailForPreview');
      router.back();
    }
  };

  useEffect(() => {
    if (source === 'genpro') {
      // Handle GenPro transformation results
      const storedUrl = sessionStorage.getItem('transformationFileUrl');
      const storedData = sessionStorage.getItem('transformationData');
      
      if (storedUrl) {
        setUrlIframe(`${URLOFFICE}${encodeURIComponent(storedUrl)}`);
      }
      
      if (storedData) {
        try {
          const transformationData = JSON.parse(storedData);
          setFileDetail({
            'File Name': 'Transformation Results',
            'File Type': 'Excel/CSV',
            'Status': 'Ready',
            'Generated': new Date().toLocaleString(),
            'Source': 'GenPro Transformation'
          });
        } catch (error) {
          console.error('Error parsing transformation data:', error);
        }
      }
    } else {
      // Handle regular file upload preview
      const fileDetailFromStorage = JSON.parse(
        localService.getItem('fileDetailForPreview'),
      );
      setFileDetail(fileDetailFromStorage);
      
      const fetchUrlForFilePreview = async () => {
        try {
          const path = `${ApiUtilities.getApiServerUrlBsm}${ApiUtilities.apiPath.getNotifications.url}/${fileId}/pre-signed-url`;
          dispatch(setIsLoading(true));
          apiService
            .getRequest(path)
            .then((res) => {
              if (res.status === 200) {
                const url = res.data.downloadUrl;
                setUrlIframe(`${URLOFFICE}${encodeURIComponent(url)}`);
              }
            })
            .finally(() => dispatch(setIsLoading(false)));
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      };
      fetchUrlForFilePreview();
    }
  }, [source]);

  return (
    <div className="flex h-[calc(100vh-70px)] flex-col">
      <div className="flex w-fit flex-row items-center space-x-2 py-2">
        <img
          onClick={goBack}
          className="h-[24px] w-[24px] cursor-pointer"
          src="/assets/images/arrow-left.svg"
          alt="back"
        />
        <span className="font-sans text-xl font-semibold leading-8 text-blueGray-300">
          {fileDetail['File Name']}
        </span>
      </div>
      <div className="flex h-[calc(100vh-120px)] flex-col overflow-auto">
        {fileDetail && (
          <div className="mb-2 w-full rounded bg-white-200 px-4 pb-4 pt-2">
            <div className="flex h-fit flex-wrap ">
              {Object.entries(fileDetail).map(([key, value]) => (
                <div className="mx-2 flex flex-col space-y-2 pt-4" key={key}>
                  <span className="font-sans text-xs font-medium text-gray-400">
                    {key}
                  </span>{' '}
                  <span className="font-sans text-sm font-semibold text-gray-500">
                    {String(value)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="grid h-[calc(100vh-40px)]">
          <iframe
            title="KPI Tree"
            width="100%"
            height="100%"
            src={urlIframe}
            frameBorder="0"
            allowFullScreen
          />
        </div>
      </div>
    </div>
  );
};

export default FilePreview;
